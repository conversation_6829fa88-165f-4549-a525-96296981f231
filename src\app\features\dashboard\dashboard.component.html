<div class="dashboard-container">
  <!-- Welcome Header -->
  <div class="dashboard-header">
    <div class="welcome-section">
      <h1 class="welcome-title">
        {{ "DASHBOARD.WELCOME" | translate }} {{ userName }}
      </h1>
      <p class="welcome-subtitle">
        {{ "DASHBOARD.WELCOME_MESSAGE" | translate }}
      </p>
      <div class="user-roles" *ngIf="userRoles.length > 0">
        <span class="role-badge" *ngFor="let role of userRoles">
          {{ role }}
        </span>
      </div>
    </div>
  </div>

  <!-- Dashboard Statistics -->
  <div class="dashboard-stats">
    <div class="stat-card">
      <div class="stat-icon">
        <svg width="26" height="24" viewBox="0 0 26 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.625 15.6738V20.1426C1.625 21.0566 2.33594 21.7676 3.25 21.7676H22.75C23.6133 21.7676 24.375 21.0566 24.375 20.1426V15.6738C24.375 15.5723 24.3242 15.4199 24.3242 15.3184V15.2676H19.1445L17.9766 17.6543C17.7227 18.2129 17.1641 18.5176 16.5547 18.5176H9.39453C8.78516 18.5176 8.22656 18.2129 7.97266 17.6543L6.80469 15.2676H1.67578V15.3184C1.625 15.4199 1.625 15.5723 1.625 15.6738ZM21.3789 3.53711C21.1758 2.77539 20.5156 2.26758 19.8047 2.26758H6.19531C5.43359 2.26758 4.77344 2.77539 4.62109 3.53711L2.08203 13.6426H6.80469C7.41406 13.6426 7.97266 13.998 8.22656 14.5566L9.39453 16.8926H16.5547L17.7227 14.5566C17.9766 13.998 18.5352 13.6426 19.1445 13.6426H23.918L21.3789 3.53711ZM0 20.1426V15.6738C0 15.4199 0 15.166 0.0507812 14.9121L2.99609 3.13086C3.40234 1.6582 4.67188 0.642578 6.19531 0.642578H19.8047C21.2773 0.642578 22.5977 1.6582 22.9531 3.13086L25.8984 14.9121C25.9492 15.166 26 15.4199 26 15.6738V20.1426C26 21.9707 24.5273 23.3926 22.75 23.3926H3.25C1.42188 23.3926 0 21.9707 0 20.1426ZM8.9375 6.33008H17.0625C17.4688 6.33008 17.875 6.73633 17.875 7.14258C17.875 7.59961 17.4688 7.95508 17.0625 7.95508H8.9375C8.48047 7.95508 8.125 7.59961 8.125 7.14258C8.125 6.73633 8.48047 6.33008 8.9375 6.33008ZM7.3125 10.3926H18.6875C19.0938 10.3926 19.5 10.7988 19.5 11.2051C19.5 11.6621 19.0938 12.0176 18.6875 12.0176H7.3125C6.85547 12.0176 6.5 11.6621 6.5 11.2051C6.5 10.7988 6.85547 10.3926 7.3125 10.3926Z" fill="currentColor"/>
        </svg>
      </div>
      <div class="stat-number">{{ dashboardStats.totalFunds }}</div>
      <div class="stat-label">{{ "DASHBOARD.STATS.TOTAL_FUNDS" | translate }}</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <svg width="26" height="24" viewBox="0 0 26 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.625 15.6738V20.1426C1.625 21.0566 2.33594 21.7676 3.25 21.7676H22.75C23.6133 21.7676 24.375 21.0566 24.375 20.1426V15.6738C24.375 15.5723 24.3242 15.4199 24.3242 15.3184V15.2676H19.1445L17.9766 17.6543C17.7227 18.2129 17.1641 18.5176 16.5547 18.5176H9.39453C8.78516 18.5176 8.22656 18.2129 7.97266 17.6543L6.80469 15.2676H1.67578V15.3184C1.625 15.4199 1.625 15.5723 1.625 15.6738ZM21.3789 3.53711C21.1758 2.77539 20.5156 2.26758 19.8047 2.26758H6.19531C5.43359 2.26758 4.77344 2.77539 4.62109 3.53711L2.08203 13.6426H6.80469C7.41406 13.6426 7.97266 13.998 8.22656 14.5566L9.39453 16.8926H16.5547L17.7227 14.5566C17.9766 13.998 18.5352 13.6426 19.1445 13.6426H23.918L21.3789 3.53711ZM0 20.1426V15.6738C0 15.4199 0 15.166 0.0507812 14.9121L2.99609 3.13086C3.40234 1.6582 4.67188 0.642578 6.19531 0.642578H19.8047C21.2773 0.642578 22.5977 1.6582 22.9531 3.13086L25.8984 14.9121C25.9492 15.166 26 15.4199 26 15.6738V20.1426C26 21.9707 24.5273 23.3926 22.75 23.3926H3.25C1.42188 23.3926 0 21.9707 0 20.1426Z" fill="currentColor"/>
        </svg>
      </div>
      <div class="stat-number">{{ dashboardStats.activeFunds }}</div>
      <div class="stat-label">{{ "DASHBOARD.STATS.ACTIVE_FUNDS" | translate }}</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="stat-number">{{ dashboardStats.pendingVotes }}</div>
      <div class="stat-label">{{ "DASHBOARD.STATS.PENDING_VOTES" | translate }}</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <svg width="29" height="30" viewBox="0 0 29 30" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4999 4.69434C11.6637 4.69434 9.3645 6.99354 9.3645 9.82975C9.3645 12.666 11.6637 14.9652 14.4999 14.9652C17.3361 14.9652 19.6353 12.666 19.6353 9.82975C19.6353 6.99354 17.3361 4.69434 14.4999 4.69434ZM11.177 9.82975C11.177 7.99456 12.6647 6.50684 14.4999 6.50684C16.3351 6.50684 17.8228 7.99456 17.8228 9.82975C17.8228 11.6649 16.3351 13.1527 14.4999 13.1527C12.6647 13.1527 11.177 11.6649 11.177 9.82975Z" fill="currentColor"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M19.7088 18.2558C16.5215 16.285 12.4787 16.285 9.29141 18.2558C9.19743 18.3139 9.07842 18.3836 8.94166 18.4636C8.32266 18.8258 7.34006 19.4008 6.66955 20.0823C6.24842 20.5104 5.8284 21.0939 5.75182 21.8215C5.6699 22.5998 6.00026 23.3203 6.63048 23.9438C7.67161 24.9738 8.9613 25.8402 10.6523 25.8402H18.3479C20.0389 25.8402 21.3286 24.9738 22.3697 23.9438C22.9999 23.3203 23.3303 22.5998 23.2484 21.8215C23.1718 21.0939 22.7518 20.5104 22.3306 20.0823C21.6601 19.4008 20.6775 18.8258 20.0585 18.4636C19.9218 18.3836 19.8027 18.3139 19.7088 18.2558ZM10.2446 19.7974C12.8478 18.1878 16.1524 18.1878 18.7555 19.7974C18.9118 19.894 19.0794 19.9928 19.2523 20.0948C19.8708 20.4595 20.5574 20.8643 21.0386 21.3534C21.3346 21.6543 21.4314 21.874 21.4458 22.0112C21.4549 22.0976 21.4506 22.3035 21.095 22.6553C20.2098 23.531 19.3541 24.0277 18.3479 24.0277H10.6523C9.64607 24.0277 8.79038 23.531 7.90523 22.6553C7.54963 22.3035 7.54527 22.0976 7.55437 22.0112C7.56881 21.874 7.66557 21.6543 7.96161 21.3534C8.44278 20.8643 9.1294 20.4595 9.74791 20.0948C9.92079 19.9928 10.0884 19.894 10.2446 19.7974Z" fill="currentColor"/>
        </svg>
      </div>
      <div class="stat-number">{{ dashboardStats.totalUsers }}</div>
      <div class="stat-label">{{ "DASHBOARD.STATS.TOTAL_USERS" | translate }}</div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="quick-actions">
    <h2 class="section-title">{{ "DASHBOARD.QUICK_ACTIONS.TITLE" | translate }}</h2>
    <div class="actions-grid">
      <div
        class="action-card"
        *ngFor="let action of quickActions"
        (click)="navigateTo(action.route)"
      >
        <div class="action-icon" [style.color]="action.color" [innerHTML]="action.svgIcon">
        </div>
        <div class="action-title">{{ action.title | translate }}</div>
      </div>
    </div>
  </div>


</div>
