<div class="dashboard-container">
  <!-- Welcome Header -->
  <div class="dashboard-header">
    <div class="welcome-section">
      <h1 class="welcome-title">
        {{ "DASHBOARD.WELCOME" | translate }} {{ userName }}
      </h1>
      <p class="welcome-subtitle">
        {{ "DASHBOARD.WELCOME_MESSAGE" | translate }}
      </p>
      <div class="user-roles" *ngIf="userRoles.length > 0">
        <span class="role-badge" *ngFor="let role of userRoles">
          {{ role }}
        </span>
      </div>
    </div>
  </div>

  <!-- Dashboard Statistics -->
  <div class="dashboard-stats">
    <div class="stat-card">
      <div class="stat-icon">
        <i class="fas fa-folder"></i>
      </div>
      <div class="stat-number">{{ dashboardStats.totalFunds }}</div>
      <div class="stat-label">{{ "DASHBOARD.STATS.TOTAL_FUNDS" | translate }}</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <i class="fas fa-chart-line"></i>
      </div>
      <div class="stat-number">{{ dashboardStats.activeFunds }}</div>
      <div class="stat-label">{{ "DASHBOARD.STATS.ACTIVE_FUNDS" | translate }}</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <i class="fas fa-vote-yea"></i>
      </div>
      <div class="stat-number">{{ dashboardStats.pendingVotes }}</div>
      <div class="stat-label">{{ "DASHBOARD.STATS.PENDING_VOTES" | translate }}</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <i class="fas fa-users"></i>
      </div>
      <div class="stat-number">{{ dashboardStats.totalUsers }}</div>
      <div class="stat-label">{{ "DASHBOARD.STATS.TOTAL_USERS" | translate }}</div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="quick-actions">
    <h2 class="section-title">{{ "DASHBOARD.QUICK_ACTIONS.TITLE" | translate }}</h2>
    <div class="actions-grid">
      <div
        class="action-card"
        *ngFor="let action of quickActions"
        (click)="navigateTo(action.route)"
      >
        <div class="action-icon" [style.color]="action.color">
          <i [class]="action.icon"></i>
        </div>
        <div class="action-title">{{ action.title | translate }}</div>
      </div>
    </div>
  </div>

  <!-- Voting Section -->
  <div class="voting-section">
    <app-voting-card></app-voting-card>
  </div>
</div>
