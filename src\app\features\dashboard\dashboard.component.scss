@import "../../../assets/scss/_variables.scss";

.dashboard-container {
  padding: 1.5rem;
  background-color: $card_bg;
  min-height: calc(100vh - 120px);
}

.dashboard-header {
  margin-bottom: 2rem;

  .welcome-section {
    background: linear-gradient(135deg, $navy-blue 0%, $dark-blue 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    
    .welcome-title {
      font-size: 1.8rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }
    
    .welcome-subtitle {
      font-size: 1rem;
      opacity: 0.9;
    }
    
    .user-roles {
      margin-top: 1rem;
      
      .role-badge {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        margin-right: 0.5rem;
        display: inline-block;
      }
    }
  }
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  
  .stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid $navy-blue;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
      font-size: 2rem;
      color: $navy-blue;
      margin-bottom: 1rem;
    }

    .stat-number {
      font-size: 2.5rem;
      font-weight: 700;
      color: $dark;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: $text-grey;
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

.quick-actions {
  margin-bottom: 2rem;
  
  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: $dark;
    margin-bottom: 1rem;
  }
  
  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    
    .action-card {
      background: white;
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 2px solid transparent;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        border-color: $navy-blue;
      }

      .action-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
      }

      .action-title {
        font-weight: 600;
        color: $dark;
        font-size: 0.875rem;
      }
    }
  }
}

.voting-section {
  margin-top: 2rem;
}

// RTL Support
:root[dir="rtl"] .dashboard-container,
html[dir="rtl"] .dashboard-container {
  .stat-card {
    border-left: none;
    border-right: 4px solid $navy-blue;
  }
  
  .user-roles .role-badge {
    margin-right: 0;
    margin-left: 0.5rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .welcome-section {
    padding: 1.5rem;
    
    .welcome-title {
      font-size: 1.5rem;
    }
  }
}
