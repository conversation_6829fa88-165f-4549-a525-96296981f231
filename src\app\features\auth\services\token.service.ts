// token.service.ts
import { Injectable } from '@angular/core';
import { jwtDecode } from 'jwt-decode';

export interface DecodedToken {
  sub: string;
  exp: number;
  iat: number;
  roles?: string[];
  Permission?: string[];
  Id?:string;
  Name?:string;
  // Add more properties if your token contains them
}
export enum userRole{
  fundManager="fundmanager",
  boardSecretary="boardsecretary",
  legalCouncil="legalcouncil"
}
@Injectable({
  providedIn: 'root',
})
export class TokenService {
  private tokenKey = 'auth_token';
  private decodedToken: DecodedToken | null = null;
  init(): void {
    this.loadToken();
  }
  private loadToken(): void {
    const token = localStorage.getItem(this.tokenKey);
    if (token) {
      this.setToken(token);
    }
  }

  setToken(token: string): void {
    localStorage.setItem(this.tokenKey, token);
    let decodedToken: any = jwtDecode<DecodedToken>(token);
    decodedToken.roles = decodedToken['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'];
    decodedToken.Name = decodedToken['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'];
    this.decodedToken = decodedToken;
  //   const rolesToStore = Array.isArray(this.decodedToken?.roles)
  //   ? JSON.stringify(this.decodedToken?.roles)
  //   : this.decodedToken?.roles;

  // localStorage.setItem('roles', rolesToStore!);
 }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }
  getroles(){
    const rolesToStore = Array.isArray(this.decodedToken?.roles) ? JSON.stringify(this.decodedToken?.roles) : this.decodedToken?.roles;
    return rolesToStore
  }
  getFullName(){
    debugger;
    return this.decodedToken?.Name;
  }
  getDecodedToken(): DecodedToken | null {
    return this.decodedToken;
  }
  getuserId(): string | undefined {
    return this.decodedToken?.Id;
  }

  hasPermission(permission: string): boolean {
    return this.decodedToken?.Permission?.find(x=>x==permission) ? true:false;
  }

  hasRole(role: string): boolean {
    return this.decodedToken?.roles?.includes(role) ?? false;
  }

  logout(): void {
    localStorage.removeItem(this.tokenKey);
    this.decodedToken = null;
  }
  isLoggedIn(): boolean {
    return !!this.getToken();
  }
}
