import { Component, OnInit, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { TokenService } from 'src/app/features/auth/services/token.service';

@Component({
  selector: 'app-voting-card',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './voting-card.component.html',
  styleUrl: './voting-card.component.scss'
})
export class VotingCardComponent implements OnInit {
  constructor(private tokenService: TokenService) {}

  userName: string = '';
  userImage: string = "assets/images/5a88f6c30078d932a34b61c983a4185389144193.jpg";
  remainingVotes: number = 3;

  ngOnInit() {
    this.userName = this.tokenService.getFullName() || 'User';
  }
}
