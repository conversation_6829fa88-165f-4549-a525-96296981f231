<div *ngIf="formGroup">
  <form [formGroup]="formGroup" #f="ngForm" novalidate name="form" class="form">
    <div class="row mb-3">
      <ng-content select="[slot='top']"></ng-content>

      <ng-container *ngFor="let control of formControls;">

        <ng-content select="[slot='between']"></ng-content>
        <ng-content select="[slot='bottom']"></ng-content>

        <div class="form-group mb-24" [ngClass]="control.class" *ngIf="control.type != inputType.empty && isControlVisible(control)">
          <label class="label">
            {{ control.label |translate}}
            <!-- <span *ngIf="control.isRequired" class="danger-color"> *</span> -->
            <span *ngIf="control.isRequired == false && isControlOptional(control)" class=" fs-14 text-grey"> {{'FORM.optional' |translate}} </span>
          </label>
          <!--************ "text" **********-->
          <ng-container *ngIf="control.type == inputType.Text">
            <arabdt-reusable-rf-text-input [disabled]="control.isReadonly || false" [appearance]="appearance.Outline"
              [controlSize]="controlSize.Medium" [autoFocus]="control.autoFocus || false"
              [readonly]="control.isReadonly || false" [formControlName]="control.formControlName"
              [placeholder]="control.placeholder" [id]="control.id" (focusEvent)="onControlFocus($event, control)"
              (blurEvent)="onControlBlur($event, control)" (keyDownEvent)="onKeyPressed($event, control)"
              (inputChangeEvent)="onValueChange($event, control)" (keypress)="allowOnlyLetters($event)"
              [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors"></arabdt-reusable-rf-text-input>
          </ng-container>
          <!--************ "text" **********-->
          <ng-container *ngIf="control.type == inputType.Mixed">
            <arabdt-reusable-rf-text-input [disabled]="control.isReadonly || false" [appearance]="appearance.Outline"
              [controlSize]="controlSize.Medium" [autoFocus]="control.autoFocus || false"
              [readonly]="control.isReadonly || false" [formControlName]="control.formControlName"
              [placeholder]="control.placeholder" [id]="control.id" (focusEvent)="onControlFocus($event, control)"
              (blurEvent)="onControlBlur($event, control)" (keyDownEvent)="onKeyPressed($event, control)"
              (inputChangeEvent)="onValueChange($event, control)"
              [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors"></arabdt-reusable-rf-text-input>
          </ng-container>

          <!--************ "password" **********-->
          <ng-container *ngIf="control.type == inputType.Password">
            <div class="password-input-container position-relative">
              <arabdt-reusable-rf-text-input
                [disabled]="control.isReadonly || false"
                [appearance]="appearance.Outline"
                [controlSize]="controlSize.Medium"
                [autoFocus]="control.autoFocus || false"
                [readonly]="control.isReadonly || false"
                [formControlName]="control.formControlName"
                [placeholder]="control.placeholder"
                [id]="control.id"
                [type]="isPasswordVisible(control.formControlName) ? 'text' : 'password'"
                (focusEvent)="onControlFocus($event, control)"
                (blurEvent)="onControlBlur($event, control)"
                (keyDownEvent)="onKeyPressed($event, control)"
                (inputChangeEvent)="onValueChange($event, control)"
                [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors">
              </arabdt-reusable-rf-text-input>

              <!-- Password toggle button -->
              <button
                type="button"
                class="btn btn-link password-toggle-btn position-absolute"
                [ngStyle]="currentLang == 'ar' ? {'left': '5px'} : {'right': '5px'}"
                (click)="togglePasswordVisibility(control.formControlName)"
                [attr.aria-label]="isPasswordVisible(control.formControlName) ? 'Hide password' : 'Show password'"

                tabindex="-1">
                <!-- <i [class]="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i> -->

                <span
                class="eye-icon position-absolute"
                style="cursor: pointer"
              >
              <img [src]="isPasswordVisible(control.formControlName) ? 'assets/images/eye-slash.png' :'assets/images/eye2.png'" alt="password" />
              </span>
              </button>

              <!-- Password strength indicator -->
              <div *ngIf="control.showStrengthIndicator && getFormGroup[control.formControlName].value"
                   class="password-strength-indicator mt-2">
                <div class="strength-bar">
                  <div class="strength-fill" [ngClass]="getPasswordStrengthClass(control)"></div>
                </div>
                <small class="strength-text" [ngClass]="getPasswordStrengthClass(control)">
                  {{ getPasswordStrengthText(control) | translate }}
                </small>
              </div>
            </div>
          </ng-container>

          <!--************ "textarea" **********-->
          <ng-container *ngIf="control.type ==inputType.Textarea">
            <textarea class="textarea form-control" [id]="control.id" [name]="control.name" [readonly]="control.isReadonly || false"
              [required]="control.isRequired || false" [formControlName]="control.formControlName"
              placeholder="" type="text" [ngClass]="{
                'is-invalid':
                  isFormSubmitted && getFormGroup[control.formControlName].errors
              }" [attr.maxlength]="control.maxLength" (blur)="onControlBlur($event, control)" rows="10"></textarea>
          </ng-container>
          <!--************ "number" **********-->
          <ng-container *ngIf="control.type == inputType.Number">
            <arabdt-reusable-rf-text-input [appearance]="appearance.Outline" [controlSize]="controlSize.Medium"
              [autoFocus]="control.autoFocus || false" [readonly]="control.isReadonly || false"
              [formControlName]="control.formControlName" [placeholder]="control.placeholder" [id]="control.id"
              (focusEvent)="onControlFocus($event, control)" (blurEvent)="onControlBlur($event, control)"
              (keyDownEvent)="onKeyPressed($event, control)" (inputChangeEvent)="onValueChange($event, control)"
              [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors" type="number"[step]="control.step"
              [minLength]="control.minLength" [maxLength]="control.maxLength"></arabdt-reusable-rf-text-input>
          </ng-container>
          <!--************ "number decimal" **********-->
          <ng-container *ngIf="control.type == inputType.NumberDecimal">
            <arabdt-reusable-rf-text-input [appearance]="appearance.Outline" [controlSize]="controlSize.Medium"
              [autoFocus]="control.autoFocus || false" [readonly]="control.isReadonly || false"
              [formControlName]="control.formControlName" [placeholder]="control.placeholder" [id]="control.id"
              (focusEvent)="onControlFocus($event, control)" (blurEvent)="onControlBlur($event, control)"
              (keyDownEvent)="onKeyPressed($event, control)" (inputChangeEvent)="onValueChange($event, control)"
              [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors" type="number"
              [minLength]="control.minLength" [maxLength]="control.maxLength"></arabdt-reusable-rf-text-input>
          </ng-container>

          <!--************ "email" **********-->
          <ng-container *ngIf="control.type == inputType.Email">
            <arabdt-reusable-rf-text-input [appearance]="appearance.Outline" [controlSize]="controlSize.Medium"
              [autoFocus]="control.autoFocus || false" [readonly]="control.isReadonly || false"
              [formControlName]="control.formControlName" [placeholder]="control.placeholder" [id]="control.id"
              (focusEvent)="onControlFocus($event, control)" (blurEvent)="onControlBlur($event, control)"
              (keyDownEvent)="onKeyPressed($event, control)" (inputChangeEvent)="onValueChange($event, control)"
              [isInvalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors" type="email"
              [minLength]="control.minLength" [maxLength]="control.maxLength"></arabdt-reusable-rf-text-input>
          </ng-container>

          <!--************ "tel" **********-->
          <ng-container *ngIf="control.type == inputType.Tel">
            <div class="tel-input-container"
                 [class.is-invalid]="isFormSubmitted && !!getFormGroup[control.formControlName].errors">
              <!-- <div class="country-code-prefix">
                <span class="country-code">+966</span>
              </div> -->
              <div class="tel-input">
                <arabdt-reusable-rf-text-input
                  [appearance]="appearance.Outline"
                  [controlSize]="controlSize.Medium"
                  [autoFocus]="control.autoFocus || false"
                  [readonly]="control.isReadonly || false"
                  [formControlName]="control.formControlName"
                  [placeholder]="control.placeholder || 'FORM.PHONE_PLACEHOLDER' | translate"
                  [id]="control.id"
                  (focusEvent)="onControlFocus($event, control)"
                  (blurEvent)="onControlBlur($event, control)"
                  (keyDownEvent)="onTelKeyPressed($event, control)"
                  (inputChangeEvent)="onTelValueChange($event, control)"
                  [isInvalid]="false"
                  type="tel"
                  [minLength]="9"
                  [maxLength]="9">
                </arabdt-reusable-rf-text-input>
              </div>
            </div>
          </ng-container>
          <!--************ "date" **********-->
          <ng-container *ngIf="control.type == inputType.Date">

            <arabdt-datepicker
            [disabled]="control.isReadonly || false"
            [calendarMode]="control.calendarMode || CalendarModeEnum.BOTH"
              [placeholder]="control.placeholder || ''  |translate " [readonly]="control.isReadonly || false" [ngClass]="{
                'is-invalid':
                  isFormSubmitted && getFormGroup[control.formControlName].errors
              }" [minGreg]="control.minGreg" [maxGreg]="control.maxGreg" [minHijri]="control.minHijri"
              [formControlName]="control.formControlName" [maxHijri]="control.maxHijri"
              (onDateChange)="onDateSelected($event, control)"></arabdt-datepicker>
          </ng-container>

          <!--************ "radio" **********-->
          <ng-container *ngIf="control.type == inputType.Radio">

            <!-- <div class="form-group"> -->
            <mat-radio-group [formControlName]="control.formControlName" class="d-flex align-items-center gap-4 mt-2"
              [ngClass]="{
                  'is-invalid':
                    isFormSubmitted && getFormGroup[control.formControlName].errors
                }" (change)="onRadioButtonChange($event, control)">
              <div *ngFor="let option of control.options">
                <mat-radio-button [value]="option.id" [id]="option.id" class="ml-4px">
                  {{ option.name |translate }}
                </mat-radio-button>
              </div>
            </mat-radio-group>
            <!-- </div> -->
          </ng-container>

          <!--************ "checkbox" **********-->
          <ng-container *ngIf="control.type == inputType.Checkbox">

            <div class="form-group w-100 p-0">
              <div *ngFor="let option of control.options" class="field-checkbox mb-8">
                <mat-checkbox [formControlName]="control.formControlName" [value]="option.id" [ngClass]="{
                    'is-invalid':
                      isFormSubmitted &&
                      getFormGroup[control.formControlName].errors
                  }" (change)="onCheckboxChange($event, control)" [id]="option.id" [disabled]="control.isReadonly || false">
                  {{ option.name |translate }}
                </mat-checkbox>
              </div>
            </div>
          </ng-container>

          <!--************ "switch" **********-->
          <ng-container *ngIf="control.type == inputType.Switch">
            <div class="form-group w-100 mt-18">
              <div *ngFor="let option of control.options" class="field-switch mb-8">
                <mat-slide-toggle [formControlName]="control.formControlName" [ngClass]="{
                    'is-invalid':
                      isFormSubmitted &&
                      getFormGroup[control.formControlName].errors
                  }" (change)="onSwitchChange($event, control)" [id]="option.id" [disabled]="control.isReadonly || false">
                  {{ option.name |translate }}
                </mat-slide-toggle>
              </div>
            </div>
          </ng-container>

          <!--************ "dropdown" **********-->
          <ng-container *ngIf="control.type == inputType.Dropdown">
            <!-- Dropdown Input -->
            <ng-select [items]="control.options || []" bindLabel="name" bindValue="id"   appendTo="body"

              [maxSelectedItems]="control.maxLength" [formControlName]="control.formControlName"
              [placeholder]="control.placeholder || '' | translate" [multiple]="control.multiple" [clearable]="true"
              [searchable]="true" [disabled]="control.isReadonly || false" [ngClass]="{
    'is-invalid': isFormSubmitted && getFormGroup[control.formControlName].errors,
    'multiple': control.multiple
  }" class="custom-ng-select" (change)="onDropdownChange($event, control)">

              <!-- Custom option template with translation -->
              <ng-option *ngFor="let option of control.options || []" [value]="option.id">
                {{ option.name | translate }}
              </ng-option>
            </ng-select>

            <!-- Selected Items Below -->
            <div class="selected-tags-container d-flex gap-2 mt-1 flex-wrap-wrap "
              *ngIf="getFormGroup[control.formControlName].value?.length && control.multiple">
              <div class="tag border-rounded " *ngFor="let selectedId of getFormGroup[control.formControlName].value">
                {{ getSelectedName(control.options ||[], selectedId) | translate }}
                <span class="remove-icon mx-1" (click)="removeSelectedItem(control, selectedId)">
                  <img src="assets/images/x.svg" alt="remove" />
                </span>
              </div>
            </div>


          </ng-container>

          <!--************ "file" **********-->
          <ng-container *ngIf="control.type == inputType.file">
            <app-file-upload [moduleId] ="control.moduleId ?? 5" [initialFiles]="control.initialFiles ??[]" [allowedTypes]="control.allowedTypes || []"
              [maxLength]="control.maxLength ||10"
              (fileUploaded)="handleFileUpload($event,control)" [maxSize]="control.max ||10" [multiple]="control.multiple || false"></app-file-upload>

          </ng-container>

          <ng-content select="[slot='last']"></ng-content>

          <!--************ "StatusBadge" **********-->
          <ng-container *ngIf="control.type === inputType.StatusBadge">
            <div class="status-badge-container">
              <span class="status-badge" [ngClass]="getStatusBadgeClass(control)">
                {{ getFormGroup[control.formControlName].value }}
              </span>
            </div>
          </ng-container>

          <ng-container *ngIf="control.type === inputType.Custom">
            <ng-container *ngTemplateOutlet="customTemplate"></ng-container>

          </ng-container>

          <app-validation-messages [formSubmitted]="isFormSubmitted || false"
            [control]="getFormGroup[control.formControlName]">
          </app-validation-messages>

        </div>
        <div *ngIf="control.type==inputType.empty" [ngClass]="control.class">
          <div class="w-100"></div>
        </div>
      </ng-container>
    </div>
  </form>
</div>
