import { Component, OnInit, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { VotingCardComponent } from './voting-card/voting-card.component';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  providers: [TokenService],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  private tokenService = inject(TokenService);
  private router = inject(Router);

  // User information
  userName: string | undefined;
  userRoles: string[] = [];

  // Dashboard stats
  dashboardStats = {
    totalFunds: 12,
    activeFunds: 8,
    pendingVotes: 3,
    totalUsers: 45,
  };

  // Quick actions with SVG icons from side menu
  quickActions = [
    {
      title: 'DASHBOARD.QUICK_ACTIONS.VIEW_FUNDS',
      svgIcon: `<svg width="26" height="24" viewBox="0 0 26 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1.625 15.6738V20.1426C1.625 21.0566 2.33594 21.7676 3.25 21.7676H22.75C23.6133 21.7676 24.375 21.0566 24.375 20.1426V15.6738C24.375 15.5723 24.3242 15.4199 24.3242 15.3184V15.2676H19.1445L17.9766 17.6543C17.7227 18.2129 17.1641 18.5176 16.5547 18.5176H9.39453C8.78516 18.5176 8.22656 18.2129 7.97266 17.6543L6.80469 15.2676H1.67578V15.3184C1.625 15.4199 1.625 15.5723 1.625 15.6738ZM21.3789 3.53711C21.1758 2.77539 20.5156 2.26758 19.8047 2.26758H6.19531C5.43359 2.26758 4.77344 2.77539 4.62109 3.53711L2.08203 13.6426H6.80469C7.41406 13.6426 7.97266 13.998 8.22656 14.5566L9.39453 16.8926H16.5547L17.7227 14.5566C17.9766 13.998 18.5352 13.6426 19.1445 13.6426H23.918L21.3789 3.53711ZM0 20.1426V15.6738C0 15.4199 0 15.166 0.0507812 14.9121L2.99609 3.13086C3.40234 1.6582 4.67188 0.642578 6.19531 0.642578H19.8047C21.2773 0.642578 22.5977 1.6582 22.9531 3.13086L25.8984 14.9121C25.9492 15.166 26 15.4199 26 15.6738V20.1426C26 21.9707 24.5273 23.3926 22.75 23.3926H3.25C1.42188 23.3926 0 21.9707 0 20.1426ZM8.9375 6.33008H17.0625C17.4688 6.33008 17.875 6.73633 17.875 7.14258C17.875 7.59961 17.4688 7.95508 17.0625 7.95508H8.9375C8.48047 7.95508 8.125 7.59961 8.125 7.14258C8.125 6.73633 8.48047 6.33008 8.9375 6.33008ZM7.3125 10.3926H18.6875C19.0938 10.3926 19.5 10.7988 19.5 11.2051C19.5 11.6621 19.0938 12.0176 18.6875 12.0176H7.3125C6.85547 12.0176 6.5 11.6621 6.5 11.2051C6.5 10.7988 6.85547 10.3926 7.3125 10.3926Z" fill="currentColor"/>
                </svg>`,
      route: '/admin/investment-funds',
      color: '#2F80ED',
    },
    {
      title: 'DASHBOARD.QUICK_ACTIONS.USER_MANAGEMENT',
      svgIcon: `<svg width="29" height="30" viewBox="0 0 29 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6.6458 7.11133C4.47694 7.11133 2.71872 8.86954 2.71872 11.0384C2.71872 13.2073 4.47694 14.9655 6.6458 14.9655C7.14631 14.9655 7.55205 14.5598 7.55205 14.0592C7.55205 13.5587 7.14631 13.153 6.6458 13.153C5.47795 13.153 4.53122 12.2063 4.53122 11.0384C4.53122 9.87056 5.47795 8.92383 6.6458 8.92383C7.14631 8.92383 7.55205 8.51809 7.55205 8.01758C7.55205 7.51707 7.14631 7.11133 6.6458 7.11133Z" fill="currentColor"/>
                  <path d="M6.9442 16.3943C6.89911 15.8958 6.45846 15.5283 5.95998 15.5733C4.82593 15.6759 3.72327 16.1122 2.75141 16.8606C2.6954 16.9037 2.62162 16.9574 2.53493 17.0205C2.12326 17.32 1.42043 17.8313 0.94675 18.4309C0.644083 18.814 0.361044 19.3154 0.310085 19.9183C0.256717 20.5497 0.468388 21.1631 0.922186 21.7222C1.6168 22.5781 2.56993 23.4238 3.898 23.4238C4.39851 23.4238 4.80425 23.0181 4.80425 22.5176C4.80425 22.0171 4.39851 21.6113 3.898 21.6113C3.41526 21.6113 2.92809 21.3176 2.32949 20.58C2.12145 20.3237 2.1084 20.1626 2.11615 20.071C2.1263 19.9509 2.19019 19.7808 2.369 19.5544C2.67017 19.1732 3.09235 18.8629 3.5027 18.5613C3.62302 18.4729 3.74232 18.3852 3.85726 18.2967C4.57062 17.7473 5.34959 17.4485 6.12329 17.3785C6.62176 17.3334 6.9893 16.8927 6.9442 16.3943Z" fill="currentColor"/>
                  <path d="M21.7499 7.11133C21.2494 7.11133 20.8437 7.51707 20.8437 8.01758C20.8437 8.51809 21.2494 8.92383 21.7499 8.92383C22.9178 8.92383 23.8645 9.87056 23.8645 11.0384C23.8645 12.2063 22.9178 13.153 21.7499 13.153C21.2494 13.153 20.8437 13.5587 20.8437 14.0592C20.8437 14.5598 21.2494 14.9655 21.7499 14.9655C23.9188 14.9655 25.677 13.2073 25.677 11.0384C25.677 8.86954 23.9188 7.11133 21.7499 7.11133Z" fill="currentColor"/>
                  <path d="M23.0399 15.5733C22.5414 15.5283 22.1008 15.8958 22.0557 16.3943C22.0106 16.8927 22.3781 17.3334 22.8766 17.3785C23.6503 17.4485 24.4293 17.7473 25.1426 18.2967C25.2575 18.3852 25.3768 18.4728 25.4971 18.5612C25.9075 18.8628 26.3297 19.1732 26.6309 19.5544C26.8097 19.7808 26.8736 19.9509 26.8837 20.071C26.8915 20.1626 26.8784 20.3237 26.6704 20.58C26.0718 21.3176 25.5846 21.6113 25.1019 21.6113C24.6014 21.6113 24.1956 22.0171 24.1956 22.5176C24.1956 23.0181 24.6014 23.4238 25.1019 23.4238C26.43 23.4238 27.3831 22.5781 28.0777 21.7222C28.5315 21.1631 28.7432 20.5497 28.6898 19.9183C28.6388 19.3154 28.3558 18.814 28.0531 18.4309C27.5795 17.8313 26.8767 17.32 26.465 17.0205C26.3784 16.9575 26.3045 16.9037 26.2485 16.8606C25.2766 16.1122 24.174 15.6759 23.0399 15.5733Z" fill="currentColor"/>
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4999 4.69434C11.6637 4.69434 9.3645 6.99354 9.3645 9.82975C9.3645 12.666 11.6637 14.9652 14.4999 14.9652C17.3361 14.9652 19.6353 12.666 19.6353 9.82975C19.6353 6.99354 17.3361 4.69434 14.4999 4.69434ZM11.177 9.82975C11.177 7.99456 12.6647 6.50684 14.4999 6.50684C16.3351 6.50684 17.8228 7.99456 17.8228 9.82975C17.8228 11.6649 16.3351 13.1527 14.4999 13.1527C12.6647 13.1527 11.177 11.6649 11.177 9.82975Z" fill="currentColor"/>
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M19.7088 18.2558C16.5215 16.285 12.4787 16.285 9.29141 18.2558C9.19743 18.3139 9.07842 18.3836 8.94166 18.4636C8.32266 18.8258 7.34006 19.4008 6.66955 20.0823C6.24842 20.5104 5.8284 21.0939 5.75182 21.8215C5.6699 22.5998 6.00026 23.3203 6.63048 23.9438C7.67161 24.9738 8.9613 25.8402 10.6523 25.8402H18.3479C20.0389 25.8402 21.3286 24.9738 22.3697 23.9438C22.9999 23.3203 23.3303 22.5998 23.2484 21.8215C23.1718 21.0939 22.7518 20.5104 22.3306 20.0823C21.6601 19.4008 20.6775 18.8258 20.0585 18.4636C19.9218 18.3836 19.8027 18.3139 19.7088 18.2558ZM10.2446 19.7974C12.8478 18.1878 16.1524 18.1878 18.7555 19.7974C18.9118 19.894 19.0794 19.9928 19.2523 20.0948C19.8708 20.4595 20.5574 20.8643 21.0386 21.3534C21.3346 21.6543 21.4314 21.874 21.4458 22.0112C21.4549 22.0976 21.4506 22.3035 21.095 22.6553C20.2098 23.531 19.3541 24.0277 18.3479 24.0277H10.6523C9.64607 24.0277 8.79038 23.531 7.90523 22.6553C7.54963 22.3035 7.54527 22.0976 7.55437 22.0112C7.56881 21.874 7.66557 21.6543 7.96161 21.3534C8.44278 20.8643 9.1294 20.4595 9.74791 20.0948C9.92079 19.9928 10.0884 19.894 10.2446 19.7974Z" fill="currentColor"/>
                </svg>`,
      route: '/admin/user-management',
      color: '#27AE60',
    },
    {
      title: 'DASHBOARD.QUICK_ACTIONS.VIEW_PROFILE',
      svgIcon: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20.501 22.2735C19.253 23.5175 17.378 23.5176 13.975 23.5176C10.573 23.5176 8.699 23.5175 7.45 22.2735C6.579 21.4055 6.34105 20.2626 6.25205 18.8136C6.22705 18.3996 6.54198 18.0436 6.95498 18.0186C7.36898 17.9946 7.725 18.3086 7.75 18.7216C7.832 20.0496 8.04496 20.7475 8.50996 21.2115C9.31996 22.0175 10.8761 22.0176 13.9771 22.0176C17.0781 22.0176 18.635 22.0175 19.444 21.2115C20.252 20.4065 20.2521 18.8556 20.2521 15.7676V9.76758C20.2521 6.67858 20.252 5.12854 19.445 4.32354C18.6351 3.51758 17.0782 3.51758 13.9775 3.51758C10.8769 3.51758 9.31892 3.51758 8.50996 4.32354C8.04396 4.78754 7.831 5.48557 7.75 6.81357C7.724 7.22757 7.36898 7.5396 6.95498 7.5166C6.54098 7.4906 6.22705 7.13458 6.25205 6.72158C6.34105 5.27358 6.578 4.13052 7.45 3.26152C8.69795 2.01758 10.5708 2.01758 13.9746 2.01758C17.3783 2.01758 19.2531 2.01758 20.5021 3.26152C21.75 4.50647 21.75 6.37443 21.75 9.76716V15.7676C21.75 19.1596 21.75 21.0285 20.501 22.2735Z" fill="currentColor"/>
                  <path d="M14.9946 19.7676C14.9946 20.3196 14.5426 20.7676 13.9906 20.7676C13.4386 20.7676 12.9896 20.3196 12.9896 19.7676C12.9896 19.2156 13.4376 18.7676 13.9896 18.7676H13.9986C14.5516 18.7676 14.9946 19.2156 14.9946 19.7676Z" fill="currentColor"/>
                  <path d="M5.52588 16.5179C5.71788 16.5179 5.90993 16.4449 6.05693 16.2979C6.34993 16.0049 6.34893 15.5298 6.05693 15.2368L4.39189 13.5749H13.0049C13.4189 13.5749 13.7549 13.2389 13.7549 12.8249C13.7549 12.4109 13.4189 12.0749 13.0049 12.0749H4.24395L6.05195 10.3039C6.34795 10.0139 6.35289 9.53889 6.06289 9.24289C5.77289 8.94689 5.29795 8.94185 5.00195 9.23185L2.75088 11.4379C2.74288 11.4459 2.73493 11.4538 2.72793 11.4618C2.06993 12.1668 2.13493 13.4429 2.64893 13.9539L4.99795 16.2988C5.14395 16.4448 5.33593 16.5179 5.52793 16.5179H5.52588Z" fill="currentColor"/>
                </svg>`,
      route: '/admin/profile',
      color: '#EAA300',
    },
  ];

  ngOnInit() {
    setTimeout(() => {
      this.loadUserInfo();
    }, 500);
  }

  private loadUserInfo() {
    this.userName = this.tokenService.getFullName();
    const roles = this.tokenService.getroles();
    if (roles) {
      try {
        this.userRoles = Array.isArray(roles) ? roles : JSON.parse(roles);
      } catch (e) {
        this.userRoles = [roles];
      }
    }
  }

  navigateTo(route: string) {
    this.router.navigate([route]);
  }
}
