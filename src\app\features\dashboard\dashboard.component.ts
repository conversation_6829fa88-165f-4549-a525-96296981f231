import { Component, OnInit, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { VotingCardComponent } from "./voting-card/voting-card.component";
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, TranslateModule, VotingCardComponent],
  providers: [TokenService],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  constructor(private tokenService: TokenService) {}

  // User information
  userName: string = '';
  userRoles: string[] = [];

  // Dashboard stats
  dashboardStats = {
    totalFunds: 12,
    activeFunds: 8,
    pendingVotes: 3,
    totalUsers: 45
  };

  // Quick actions
  quickActions = [
    {
      title: 'DASHBOARD.QUICK_ACTIONS.VIEW_FUNDS',
      icon: 'fas fa-folder',
      route: '/admin/investment-funds',
      color: '#2F80ED'
    },
    {
      title: 'DASHBOARD.QUICK_ACTIONS.USER_MANAGEMENT',
      icon: 'fas fa-users',
      route: '/admin/user-management',
      color: '#27AE60'
    },
    {
      title: 'DASHBOARD.QUICK_ACTIONS.VIEW_PROFILE',
      icon: 'fas fa-user',
      route: '/admin/profile',
      color: '#EAA300'
    }
  ];

  ngOnInit() {
    this.loadUserInfo();
  }

  private loadUserInfo() {
    this.userName = this.tokenService.getFullName() || 'User';
    const roles = this.tokenService.getroles();
    if (roles) {
      try {
        this.userRoles = Array.isArray(roles) ? roles : JSON.parse(roles);
      } catch (e) {
        this.userRoles = [roles];
      }
    }
  }

  navigateTo(route: string) {
    this.router.navigate([route]);
  }
}
